/*!
 * Zoho-Inspired Theme Enhancements for Nestko
 * Modern, vibrant, and professional UI components
 * Supports both light and dark modes
 */

/* Dashboard Components */
.dashboard-card {
  background: var(--zoho-surface);
  border: 1px solid var(--bs-border-color);
  border-radius: var(--bs-border-radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.dashboard-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--zoho-primary), var(--zoho-success));
}

.dashboard-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.dashboard-stat {
  text-align: center;
  padding: 1rem;
}

.dashboard-stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--zoho-primary);
  line-height: 1;
  margin-bottom: 0.5rem;
}

.dashboard-stat-label {
  font-size: 0.875rem;
  color: var(--zoho-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 500;
}

/* Task List Components */
.task-list {
  background: var(--zoho-surface);
  border-radius: var(--bs-border-radius-lg);
  box-shadow: var(--shadow);
  overflow: hidden;
}

.task-item {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--bs-border-color);
  transition: all var(--transition-fast);
  cursor: pointer;
  position: relative;
}

.task-item:last-child {
  border-bottom: none;
}

.task-item:hover {
  background: var(--zoho-primary-light);
  transform: translateX(4px);
}

.task-item.completed {
  opacity: 0.7;
}

.task-item.completed .task-title {
  text-decoration: line-through;
}

.task-priority {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 0.75rem;
}

.task-priority.high {
  background: var(--zoho-danger);
}

.task-priority.medium {
  background: var(--zoho-warning);
}

.task-priority.low {
  background: var(--zoho-success);
}

.task-title {
  font-weight: 500;
  color: var(--bs-heading-color);
  margin-bottom: 0.25rem;
}

.task-meta {
  font-size: 0.75rem;
  color: var(--zoho-muted);
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Calendar Components */
.calendar-container {
  background: var(--zoho-surface);
  border-radius: var(--bs-border-radius-lg);
  box-shadow: var(--shadow);
  overflow: hidden;
}

.calendar-header {
  background: linear-gradient(135deg, var(--zoho-primary), var(--zoho-primary-hover));
  color: white;
  padding: 1.5rem;
  text-align: center;
}

.calendar-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.calendar-nav button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: var(--bs-border-radius);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.calendar-nav button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.calendar-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.fc-event {
  border-radius: var(--bs-border-radius) !important;
  border: none !important;
  padding: 0.25rem 0.5rem !important;
  font-size: 0.75rem !important;
  font-weight: 500 !important;
}

.fc-event-main {
  color: white !important;
}

/* Notification Panel */
.notification-panel {
  background: var(--zoho-surface);
  border-radius: var(--bs-border-radius-lg);
  box-shadow: var(--shadow);
  max-height: 400px;
  overflow-y: auto;
}

.notification-item {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--bs-border-color);
  transition: all var(--transition-fast);
  cursor: pointer;
  position: relative;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item:hover {
  background: var(--zoho-primary-light);
}

.notification-item.unread {
  background: var(--zoho-primary-light);
}

.notification-item.unread::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--zoho-primary);
}

.notification-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  font-size: 1.125rem;
}

.notification-icon.success {
  background: var(--zoho-success-light);
  color: var(--zoho-success);
}

.notification-icon.warning {
  background: var(--zoho-warning-light);
  color: var(--zoho-warning);
}

.notification-icon.danger {
  background: var(--zoho-danger-light);
  color: var(--zoho-danger);
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 500;
  color: var(--bs-heading-color);
  margin-bottom: 0.25rem;
  font-size: 0.875rem;
}

.notification-text {
  color: var(--zoho-muted);
  font-size: 0.75rem;
  line-height: 1.4;
}

.notification-time {
  color: var(--zoho-muted);
  font-size: 0.625rem;
  margin-top: 0.25rem;
}

/* Sidebar Navigation */
.sidebar {
  background: var(--zoho-surface);
  border-right: 1px solid var(--bs-border-color);
  transition: all var(--transition-normal);
}

.sidebar-nav {
  padding: 1rem 0;
}

.nav-item {
  margin-bottom: 0.25rem;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  color: var(--zoho-muted);
  text-decoration: none;
  transition: all var(--transition-fast);
  border-radius: 0 var(--bs-border-radius-lg) var(--bs-border-radius-lg) 0;
  margin-right: 1rem;
  position: relative;
}

.nav-link:hover {
  background: var(--zoho-primary-light);
  color: var(--zoho-primary);
  transform: translateX(4px);
}

.nav-link.active {
  background: var(--zoho-primary-light);
  color: var(--zoho-primary);
  font-weight: 500;
}

.nav-link.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--zoho-primary);
  border-radius: 0 4px 4px 0;
}

.nav-icon {
  margin-right: 0.75rem;
  font-size: 1.125rem;
  width: 20px;
  text-align: center;
}

/* Enhanced Buttons */
.btn-zoho-primary {
  background: linear-gradient(135deg, var(--zoho-primary), var(--zoho-primary-hover));
  border: none;
  color: white;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  border-radius: var(--bs-border-radius);
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.btn-zoho-primary:hover {
  background: linear-gradient(135deg, var(--zoho-primary-hover), var(--zoho-primary));
  transform: translateY(-1px);
  box-shadow: var(--shadow);
  color: white;
}

.btn-zoho-success {
  background: linear-gradient(135deg, var(--zoho-success), #099e46);
  border: none;
  color: white;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  border-radius: var(--bs-border-radius);
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.btn-zoho-success:hover {
  background: linear-gradient(135deg, #099e46, var(--zoho-success));
  transform: translateY(-1px);
  box-shadow: var(--shadow);
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-card {
    padding: 1rem;
  }
  
  .task-item {
    padding: 0.75rem 1rem;
  }
  
  .notification-item {
    padding: 0.75rem 1rem;
  }
  
  .nav-link {
    padding: 0.5rem 1rem;
    margin-right: 0.5rem;
  }
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus states for accessibility */
.btn:focus,
.form-control:focus,
.nav-link:focus {
  outline: 2px solid var(--zoho-primary);
  outline-offset: 2px;
}
