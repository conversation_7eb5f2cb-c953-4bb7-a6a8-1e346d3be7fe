/**
 * Config
 * -------------------------------------------------------------------------------------
 * ! IMPORTANT: Make sure you clear the browser local storage In order to see the config changes in the template.
 * ! To clear local storage: (https://www.leadshook.com/help/how-to-clear-local-storage-in-google-chrome-browser/).
 */

'use strict';

// JS global variables
let config = {
  colors: {
    primary: '#696cff',
    secondary: '#8592a3',
    success: '#71dd37',
    info: '#03c3ec',
    warning: '#ffab00',
    danger: '#ff3e1d',
    dark: '#233446',
    black: '#000',
    white: '#fff',
    body: '#f4f5fb',
    headingColor: '#566a7f',
    axisColor: '#a1acb8',
    borderColor: '#eceef1'
  }
};
Classic Theme Color Configuration - Zoho Inspired
let classicConfig = {
  colors: {
    primary: '#0069b3',        // Zoho deep blue
    secondary: '#e5f2f9',      // Zoho soft blue
    success: '#0baf4f',        // Zoho success green
    info: '#0069b3',           // Zoho deep blue
    warning: '#f7c000',        // Zoho alert yellow
    danger: '#e61c23',         // Zoho vibrant red
    dark: '#2c3e50',           // Professional dark
    black: '#1a1a1a',          // Rich black
    white: '#ffffff',          // Pure white
    body: '#f8fafc',           // Clean light background
    headingColor: '#2c3e50',   // Professional heading color
    axisColor: '#e2e8f0',      // Subtle axis color
    borderColor: '#e2e8f0',    // Clean border color
    // Additional Zoho-inspired colors
    accent: '#0069b3',         // Primary accent
    muted: '#64748b',          // Muted text
    background: '#f8fafc',     // Main background
    surface: '#ffffff',        // Card/surface background
    neutral: '#f0f0f0',        // Neutral states
    softBlue: '#e5f2f9',       // Soft blue backgrounds
    lightGray: '#f0f0f0'       // Light gray for neutral states
  }
};

// Function to get current theme config
function getThemeConfig() {
  const isClassicTheme = document.body.classList.contains('classic-theme') ||
                        window.location.search.includes('theme=old') ||
                        localStorage.getItem('active_theme') === 'old';

  return isClassicTheme ? classicConfig : config;
}
function addDebouncedEventListener(selector, event, handler, delay = 300) {
  const debounce = (func, delay) => {
    let timer;
    return function (...args) {
      clearTimeout(timer);
      timer = setTimeout(() => func.apply(this, args), delay);
    };
  };

  $(selector).on(event, debounce(handler, delay));
}
