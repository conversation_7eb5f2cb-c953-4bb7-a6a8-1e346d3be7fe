/**
 * Config
 * -------------------------------------------------------------------------------------
 * ! IMPORTANT: Make sure you clear the browser local storage In order to see the config changes in the template.
 * ! To clear local storage: (https://www.leadshook.com/help/how-to-clear-local-storage-in-google-chrome-browser/).
 */

'use strict';

// JS global variables
let config = {
  colors: {
    primary: '#696cff',
    secondary: '#8592a3',
    success: '#71dd37',
    info: '#03c3ec',
    warning: '#ffab00',
    danger: '#ff3e1d',
    dark: '#233446',
    black: '#000',
    white: '#fff',
    body: '#f4f5fb',
    headingColor: '#566a7f',
    axisColor: '#a1acb8',
    borderColor: '#eceef1'
  }
};
Classic Theme Color Configuration
let classicConfig = {
  colors: {
    primary: '#D9441B',
    secondary: '#3A6F63',
    success: '#3A6F63',
    info: '#3A6F63',
    warning: '#F4B400',
    danger: '#D9441B',
    dark: '#2B2B2B',
    black: '#000',
    white: '#fff',
    body: '#EDE9E0',
    headingColor: '#8F2C0A',
    axisColor: '#DDD2C0',
    borderColor: '#DDD2C0'
  }
};

// Function to get current theme config
function getThemeConfig() {
  const isClassicTheme = document.body.classList.contains('classic-theme') ||
                        window.location.search.includes('theme=old') ||
                        localStorage.getItem('active_theme') === 'old';

  return isClassicTheme ? classicConfig : config;
}
function addDebouncedEventListener(selector, event, handler, delay = 300) {
  const debounce = (func, delay) => {
    let timer;
    return function (...args) {
      clearTimeout(timer);
      timer = setTimeout(() => func.apply(this, args), delay);
    };
  };

  $(selector).on(event, debounce(handler, delay));
}
