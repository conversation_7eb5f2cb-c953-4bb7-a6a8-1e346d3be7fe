<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Old Theme Test - Zoho Inspired</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Boxicons -->
    <link href="https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css" rel="stylesheet">
    
    <!-- OLD THEME CSS FILES -->
    <link rel="stylesheet" href="/assets/front-end/assets/old/css/theme.css">
    <link rel="stylesheet" href="/assets/front-end/assets/old/css/zoho-theme-enhancements.css">
    
    <style>
        body {
            margin: 0;
            padding: 0;
        }
        
        .main-content {
            padding: 2rem 0;
            min-height: calc(100vh - 200px);
        }
        
        .demo-section {
            margin-bottom: 3rem;
        }
    </style>
</head>
<body>
    <!-- Demo Header -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bx bx-task me-2"></i>
                Nestko - Old Theme
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#"><i class="bx bx-home me-1"></i>Dashboard</a>
                <a class="nav-link" href="#"><i class="bx bx-task me-1"></i>Tasks</a>
                <a class="nav-link" href="#"><i class="bx bx-calendar me-1"></i>Calendar</a>
                <a class="nav-link" href="#"><i class="bx bx-user me-1"></i>Profile</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <h1 class="text-center mb-5">Old Theme with Zoho Colors</h1>
                    
                    <!-- Status Alert -->
                    <div class="alert alert-info text-center mb-5">
                        <i class="bx bx-info-circle me-2"></i>
                        This is the <strong>OLD THEME</strong> with Zoho-inspired colors and Inter font
                    </div>
                </div>
            </div>

            <!-- Typography Demo -->
            <div class="demo-section">
                <h2>Typography & Headers</h2>
                <h1>Heading 1 - Inter Font</h1>
                <h2>Heading 2 - Inter Font</h2>
                <h3>Heading 3 - Inter Font</h3>
                <p>This is body text using Inter font family. It should be clean, readable, and professional.</p>
            </div>

            <!-- Buttons Demo -->
            <div class="demo-section">
                <h3>Buttons with Zoho Colors</h3>
                <div class="row">
                    <div class="col-12">
                        <button class="btn btn-primary me-2 mb-2">Primary (Zoho Blue)</button>
                        <button class="btn btn-success me-2 mb-2">Success (Zoho Green)</button>
                        <button class="btn btn-warning me-2 mb-2">Warning (Zoho Yellow)</button>
                        <button class="btn btn-danger me-2 mb-2">Danger (Zoho Red)</button>
                        <button class="btn btn-info me-2 mb-2">Info (Zoho Blue)</button>
                    </div>
                </div>
            </div>

            <!-- Cards Demo -->
            <div class="demo-section">
                <h3>Dashboard Cards</h3>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="card dashboard-card">
                            <div class="card-body text-center">
                                <div class="dashboard-stat">
                                    <div class="dashboard-stat-number text-primary">1,234</div>
                                    <div class="dashboard-stat-label">Total Tasks</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card dashboard-card">
                            <div class="card-body text-center">
                                <div class="dashboard-stat">
                                    <div class="dashboard-stat-number text-success">567</div>
                                    <div class="dashboard-stat-label">Completed</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card dashboard-card">
                            <div class="card-body text-center">
                                <div class="dashboard-stat">
                                    <div class="dashboard-stat-number text-warning">89</div>
                                    <div class="dashboard-stat-label">In Progress</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card dashboard-card">
                            <div class="card-body text-center">
                                <div class="dashboard-stat">
                                    <div class="dashboard-stat-number text-danger">12</div>
                                    <div class="dashboard-stat-label">Overdue</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Alerts Demo -->
            <div class="demo-section">
                <h3>Alerts with Zoho Colors</h3>
                <div class="alert alert-success" role="alert">
                    <i class="bx bx-check-circle me-2"></i>
                    <strong>Success!</strong> This alert uses Zoho green color (#0baf4f)
                </div>
                <div class="alert alert-warning" role="alert">
                    <i class="bx bx-error me-2"></i>
                    <strong>Warning!</strong> This alert uses Zoho yellow color (#f7c000)
                </div>
                <div class="alert alert-danger" role="alert">
                    <i class="bx bx-x-circle me-2"></i>
                    <strong>Error!</strong> This alert uses Zoho red color (#e61c23)
                </div>
                <div class="alert alert-info" role="alert">
                    <i class="bx bx-info-circle me-2"></i>
                    <strong>Info!</strong> This alert uses Zoho blue color (#0069b3)
                </div>
            </div>

            <!-- Task List Demo -->
            <div class="demo-section">
                <h3>Task List</h3>
                <div class="task-list">
                    <div class="task-item">
                        <span class="task-priority high"></span>
                        <div class="task-content">
                            <div class="task-title">Complete project proposal</div>
                            <div class="task-meta">
                                <span><i class="bx bx-calendar me-1"></i>Due: Today</span>
                                <span><i class="bx bx-flag me-1"></i>Priority: High</span>
                            </div>
                        </div>
                    </div>
                    <div class="task-item">
                        <span class="task-priority medium"></span>
                        <div class="task-content">
                            <div class="task-title">Review team feedback</div>
                            <div class="task-meta">
                                <span><i class="bx bx-calendar me-1"></i>Due: Tomorrow</span>
                                <span><i class="bx bx-flag me-1"></i>Priority: Medium</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Demo -->
            <div class="demo-section">
                <h3>Form Elements</h3>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="taskName" class="form-label">Task Name</label>
                            <input type="text" class="form-control" id="taskName" placeholder="Enter task name">
                        </div>
                        <div class="mb-3">
                            <label for="taskDescription" class="form-label">Description</label>
                            <textarea class="form-control" id="taskDescription" rows="3" placeholder="Enter task description"></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">Create Task</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Demo Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>Nestko Task Management</h5>
                    <p>Professional task management with Zoho-inspired design</p>
                </div>
                <div class="col-md-6 text-end">
                    <p>&copy; 2024 Nestko. All rights reserved.</p>
                    <a href="#" class="me-3">Privacy Policy</a>
                    <a href="#" class="me-3">Terms of Service</a>
                    <a href="#">Contact</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- OLD THEME ACTIVATOR -->
    <script src="/assets/front-end/assets/old/js/old-theme-activator.js"></script>
    
    <!-- Force old theme for this demo -->
    <script>
        // Force old theme activation
        localStorage.setItem('active_theme', 'old');
        document.body.classList.add('old-theme');
        
        // Add URL parameter
        if (!window.location.search.includes('theme=old')) {
            const url = new URL(window.location);
            url.searchParams.set('theme', 'old');
            window.history.replaceState({}, '', url);
        }
        
        console.log('Old theme demo page loaded');
    </script>
</body>
</html>
