<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zoho Theme Test - Nestko</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Boxicons -->
    <link href="https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css" rel="stylesheet">
    
    <!-- Main Theme CSS -->
    <link rel="stylesheet" href="/assets/front-end/css/theme.css">
    
    <!-- Zoho Theme Enhancements -->
    <link rel="stylesheet" href="/assets/front-end/assets/old/css/zoho-theme-enhancements.css">
    
    <style>
        body {
            font-family: "Inter", "Segoe UI", system-ui, -apple-system, sans-serif;
            background-color: #f8fafc;
            padding: 2rem 0;
        }
        
        .demo-section {
            margin-bottom: 3rem;
        }
        
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <!-- Theme Toggle Button -->
    <div class="theme-toggle">
        <button id="themeToggle" class="btn btn-outline-secondary">
            <i class="bx bx-palette"></i> Toggle Classic Theme
        </button>
    </div>

    <div class="container">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-5">Zoho-Inspired Theme Demo</h1>
                
                <!-- Theme Status -->
                <div class="alert alert-info text-center" id="themeStatus">
                    Current Theme: <span id="currentTheme">New Theme</span>
                </div>
            </div>
        </div>

        <!-- Buttons Demo -->
        <div class="demo-section">
            <h3>Buttons</h3>
            <div class="row">
                <div class="col-12">
                    <button class="btn btn-primary me-2 mb-2">Primary Button</button>
                    <button class="btn btn-success me-2 mb-2">Success Button</button>
                    <button class="btn btn-warning me-2 mb-2">Warning Button</button>
                    <button class="btn btn-danger me-2 mb-2">Danger Button</button>
                    <button class="btn btn-info me-2 mb-2">Info Button</button>
                </div>
            </div>
        </div>

        <!-- Cards Demo -->
        <div class="demo-section">
            <h3>Dashboard Cards</h3>
            <div class="row">
                <div class="col-md-3 mb-3">
                    <div class="card dashboard-card">
                        <div class="card-body text-center">
                            <div class="dashboard-stat">
                                <div class="dashboard-stat-number">1,234</div>
                                <div class="dashboard-stat-label">Total Tasks</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card dashboard-card">
                        <div class="card-body text-center">
                            <div class="dashboard-stat">
                                <div class="dashboard-stat-number">567</div>
                                <div class="dashboard-stat-label">Completed</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card dashboard-card">
                        <div class="card-body text-center">
                            <div class="dashboard-stat">
                                <div class="dashboard-stat-number">89</div>
                                <div class="dashboard-stat-label">In Progress</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card dashboard-card">
                        <div class="card-body text-center">
                            <div class="dashboard-stat">
                                <div class="dashboard-stat-number">12</div>
                                <div class="dashboard-stat-label">Overdue</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alerts Demo -->
        <div class="demo-section">
            <h3>Alerts</h3>
            <div class="alert alert-success" role="alert">
                <i class="bx bx-check-circle me-2"></i>
                This is a success alert with Zoho green color!
            </div>
            <div class="alert alert-warning" role="alert">
                <i class="bx bx-error me-2"></i>
                This is a warning alert with Zoho yellow color!
            </div>
            <div class="alert alert-danger" role="alert">
                <i class="bx bx-x-circle me-2"></i>
                This is a danger alert with Zoho red color!
            </div>
            <div class="alert alert-info" role="alert">
                <i class="bx bx-info-circle me-2"></i>
                This is an info alert with Zoho blue color!
            </div>
        </div>

        <!-- Task List Demo -->
        <div class="demo-section">
            <h3>Task List</h3>
            <div class="task-list">
                <div class="task-item">
                    <span class="task-priority high"></span>
                    <div class="task-content">
                        <div class="task-title">Complete project proposal</div>
                        <div class="task-meta">
                            <span><i class="bx bx-calendar me-1"></i>Due: Today</span>
                            <span><i class="bx bx-flag me-1"></i>Priority: High</span>
                        </div>
                    </div>
                </div>
                <div class="task-item">
                    <span class="task-priority medium"></span>
                    <div class="task-content">
                        <div class="task-title">Review team feedback</div>
                        <div class="task-meta">
                            <span><i class="bx bx-calendar me-1"></i>Due: Tomorrow</span>
                            <span><i class="bx bx-flag me-1"></i>Priority: Medium</span>
                        </div>
                    </div>
                </div>
                <div class="task-item completed">
                    <span class="task-priority low"></span>
                    <div class="task-content">
                        <div class="task-title">Update documentation</div>
                        <div class="task-meta">
                            <span><i class="bx bx-calendar me-1"></i>Completed</span>
                            <span><i class="bx bx-flag me-1"></i>Priority: Low</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Badges Demo -->
        <div class="demo-section">
            <h3>Badges</h3>
            <span class="badge bg-primary me-2">Primary</span>
            <span class="badge bg-success me-2">Success</span>
            <span class="badge bg-warning me-2">Warning</span>
            <span class="badge bg-danger me-2">Danger</span>
            <span class="badge bg-info me-2">Info</span>
        </div>

        <!-- Form Demo -->
        <div class="demo-section">
            <h3>Form Elements</h3>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="taskName" class="form-label">Task Name</label>
                        <input type="text" class="form-control" id="taskName" placeholder="Enter task name">
                    </div>
                    <div class="mb-3">
                        <label for="taskDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="taskDescription" rows="3" placeholder="Enter task description"></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">Create Task</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Classic Theme Activator -->
    <script src="/assets/js/classic-theme-activator.js"></script>
    
    <script>
        // Theme toggle functionality
        document.getElementById('themeToggle').addEventListener('click', function() {
            const isClassic = localStorage.getItem('active_theme') === 'old';
            const newTheme = isClassic ? 'new' : 'old';
            
            localStorage.setItem('active_theme', newTheme);
            
            // Add URL parameter to simulate theme switching
            const url = new URL(window.location);
            if (newTheme === 'old') {
                url.searchParams.set('theme', 'old');
                document.body.classList.add('classic-theme');
            } else {
                url.searchParams.delete('theme');
                document.body.classList.remove('classic-theme');
            }
            
            // Update URL without reload
            window.history.replaceState({}, '', url);
            
            // Trigger theme update
            if (window.ClassicThemeActivator) {
                window.ClassicThemeActivator.applyClassicTheme();
            }
            
            updateThemeStatus();
        });
        
        function updateThemeStatus() {
            const isClassic = document.body.classList.contains('classic-theme') || 
                             window.location.search.includes('theme=old') ||
                             localStorage.getItem('active_theme') === 'old';
            
            document.getElementById('currentTheme').textContent = isClassic ? 'Classic Theme (Zoho-Inspired)' : 'New Theme';
            
            const statusAlert = document.getElementById('themeStatus');
            statusAlert.className = isClassic ? 'alert alert-primary text-center' : 'alert alert-info text-center';
        }
        
        // Initialize theme status
        updateThemeStatus();
        
        // Update status periodically
        setInterval(updateThemeStatus, 1000);
    </script>
</body>
</html>
