/*!
 * Classic Theme Activator
 * Applies Zoho-inspired styling when classic theme is active
 */

(function() {
    'use strict';

    // Function to check if classic theme is active
    function isClassicTheme() {
        return document.body.classList.contains('classic-theme') ||
               window.location.search.includes('theme=old') ||
               localStorage.getItem('active_theme') === 'old';
    }

    // Function to apply classic theme
    function applyClassicTheme() {
        if (isClassicTheme()) {
            document.body.classList.add('classic-theme');
            document.documentElement.setAttribute('data-theme', 'classic');
            
            // Update any existing charts or components
            updateChartsForClassicTheme();
            updateComponentsForClassicTheme();
        } else {
            document.body.classList.remove('classic-theme');
            document.documentElement.removeAttribute('data-theme');
        }
    }

    // Function to update charts with classic theme colors
    function updateChartsForClassicTheme() {
        // Update ApexCharts if they exist
        if (window.ApexCharts && window.Apex) {
            window.Apex.colors = ['#0069b3', '#0baf4f', '#f7c000', '#e61c23', '#6366f1', '#8b5cf6'];
            
            // Trigger chart updates
            const charts = document.querySelectorAll('.apexcharts-canvas');
            charts.forEach(chart => {
                const chartInstance = chart._chartInstance;
                if (chartInstance) {
                    chartInstance.updateOptions({
                        colors: ['#0069b3', '#0baf4f', '#f7c000', '#e61c23', '#6366f1', '#8b5cf6']
                    });
                }
            });
        }
    }

    // Function to update components with classic theme styling
    function updateComponentsForClassicTheme() {
        // Update buttons
        const buttons = document.querySelectorAll('.btn-primary');
        buttons.forEach(btn => {
            if (isClassicTheme()) {
                btn.style.backgroundColor = '#0069b3';
                btn.style.borderColor = '#0069b3';
            }
        });

        // Update cards
        const cards = document.querySelectorAll('.card');
        cards.forEach(card => {
            if (isClassicTheme()) {
                card.style.boxShadow = '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)';
                card.style.borderRadius = '0.75rem';
            }
        });

        // Update alerts
        const alerts = document.querySelectorAll('.alert-success');
        alerts.forEach(alert => {
            if (isClassicTheme()) {
                alert.style.backgroundColor = '#dcfce7';
                alert.style.borderColor = '#0baf4f';
                alert.style.color = '#166534';
            }
        });
    }

    // Function to handle theme switching
    function handleThemeSwitch() {
        // Listen for theme change events
        document.addEventListener('click', function(e) {
            // Check if it's a theme switch button
            if (e.target.matches('[data-theme-switch]') || 
                e.target.closest('[data-theme-switch]') ||
                e.target.matches('.theme-option') ||
                e.target.closest('.theme-option')) {
                
                setTimeout(() => {
                    applyClassicTheme();
                }, 100);
            }
        });

        // Listen for form submissions that might change theme
        document.addEventListener('submit', function(e) {
            if (e.target.matches('form[data-theme-form]') || 
                e.target.querySelector('select[name="active_theme"]') ||
                e.target.querySelector('input[name="active_theme"]')) {
                
                setTimeout(() => {
                    applyClassicTheme();
                }, 500);
            }
        });

        // Listen for AJAX theme changes
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            return originalFetch.apply(this, args).then(response => {
                if (args[0] && args[0].includes('theme')) {
                    setTimeout(() => {
                        applyClassicTheme();
                    }, 100);
                }
                return response;
            });
        };

        // Listen for jQuery AJAX if it exists
        if (window.jQuery) {
            $(document).ajaxComplete(function(event, xhr, settings) {
                if (settings.url && settings.url.includes('theme')) {
                    setTimeout(() => {
                        applyClassicTheme();
                    }, 100);
                }
            });
        }
    }

    // Function to add enhanced styling
    function addEnhancedStyling() {
        const style = document.createElement('style');
        style.id = 'classic-theme-enhancements';
        style.textContent = `
            /* Classic Theme Enhancements */
            body.classic-theme .card {
                background: var(--zoho-surface, #ffffff);
                border: 1px solid var(--gohub-gray-300, #e2e8f0);
                border-radius: 0.75rem;
                box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
                transition: all 0.25s ease;
            }

            body.classic-theme .card:hover {
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
                transform: translateY(-1px);
            }

            body.classic-theme .btn-primary {
                background: linear-gradient(135deg, #0069b3, #0056a3);
                border-color: #0069b3;
                font-weight: 500;
                border-radius: 0.5rem;
                transition: all 0.15s ease;
            }

            body.classic-theme .btn-primary:hover {
                background: linear-gradient(135deg, #0056a3, #0069b3);
                transform: translateY(-1px);
                box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            }

            body.classic-theme .btn-success {
                background: linear-gradient(135deg, #0baf4f, #099e46);
                border-color: #0baf4f;
            }

            body.classic-theme .btn-warning {
                background: linear-gradient(135deg, #f7c000, #e6ad00);
                border-color: #f7c000;
            }

            body.classic-theme .btn-danger {
                background: linear-gradient(135deg, #e61c23, #d1161d);
                border-color: #e61c23;
            }

            body.classic-theme .alert-success {
                background-color: #dcfce7;
                border-color: #0baf4f;
                color: #166534;
            }

            body.classic-theme .alert-warning {
                background-color: #fef3c7;
                border-color: #f7c000;
                color: #92400e;
            }

            body.classic-theme .alert-danger {
                background-color: #fee2e2;
                border-color: #e61c23;
                color: #991b1b;
            }

            body.classic-theme .alert-info {
                background-color: #e5f2f9;
                border-color: #0069b3;
                color: #1e40af;
            }

            body.classic-theme .form-control:focus {
                border-color: #0069b3;
                box-shadow: 0 0 0 0.2rem rgba(0, 105, 179, 0.25);
            }

            body.classic-theme .nav-link.active {
                color: #0069b3;
                background-color: #e5f2f9;
            }

            body.classic-theme .badge-primary {
                background-color: #0069b3;
            }

            body.classic-theme .badge-success {
                background-color: #0baf4f;
            }

            body.classic-theme .badge-warning {
                background-color: #f7c000;
            }

            body.classic-theme .badge-danger {
                background-color: #e61c23;
            }

            body.classic-theme .text-primary {
                color: #0069b3 !important;
            }

            body.classic-theme .text-success {
                color: #0baf4f !important;
            }

            body.classic-theme .text-warning {
                color: #f7c000 !important;
            }

            body.classic-theme .text-danger {
                color: #e61c23 !important;
            }

            body.classic-theme .bg-primary {
                background-color: #0069b3 !important;
            }

            body.classic-theme .bg-success {
                background-color: #0baf4f !important;
            }

            body.classic-theme .bg-warning {
                background-color: #f7c000 !important;
            }

            body.classic-theme .bg-danger {
                background-color: #e61c23 !important;
            }

            /* Dashboard specific styling */
            body.classic-theme .dashboard-card {
                background: var(--zoho-surface, #ffffff);
                border: 1px solid var(--gohub-gray-300, #e2e8f0);
                border-radius: 0.75rem;
                padding: 1.5rem;
                box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
                transition: all 0.25s ease;
                position: relative;
                overflow: hidden;
            }

            body.classic-theme .dashboard-card::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 4px;
                background: linear-gradient(90deg, #0069b3, #0baf4f);
            }
        `;
        
        // Remove existing style if it exists
        const existingStyle = document.getElementById('classic-theme-enhancements');
        if (existingStyle) {
            existingStyle.remove();
        }
        
        document.head.appendChild(style);
    }

    // Initialize when DOM is ready
    function init() {
        addEnhancedStyling();
        applyClassicTheme();
        handleThemeSwitch();
        
        // Check theme periodically in case it changes
        setInterval(applyClassicTheme, 1000);
    }

    // Run initialization
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // Export for global access
    window.ClassicThemeActivator = {
        applyClassicTheme,
        isClassicTheme,
        updateChartsForClassicTheme,
        updateComponentsForClassicTheme
    };

})();
