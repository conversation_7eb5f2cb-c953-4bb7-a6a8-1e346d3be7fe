/*!
 * Old Theme Activator for Zoho-Inspired Design
 * Applies styling specifically when the old theme is active
 */

(function() {
    'use strict';

    // Function to check if old theme is active
    function isOldThemeActive() {
        return window.location.search.includes('theme=old') ||
               localStorage.getItem('active_theme') === 'old' ||
               document.body.classList.contains('old-theme') ||
               document.body.classList.contains('classic-theme');
    }

    // Function to apply old theme styling
    function applyOldTheme() {
        if (isOldThemeActive()) {
            document.body.classList.add('old-theme');
            console.log('Old theme activated with Zoho styling');
            
            // Apply immediate styling
            applyHeaderStyling();
            applyFooterStyling();
            applyTypography();
            applyComponentStyling();
        }
    }

    // Apply header styling
    function applyHeaderStyling() {
        const headers = document.querySelectorAll('.navbar, .header, [class*="header"], .top-bar, .app-brand, .layout-navbar');
        headers.forEach(header => {
            header.style.background = 'linear-gradient(135deg, #0069b3, #0056a3)';
            header.style.color = '#ffffff';
            header.style.borderBottom = '1px solid rgba(255, 255, 255, 0.1)';
            header.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
            
            // Style all child elements
            const children = header.querySelectorAll('*');
            children.forEach(child => {
                child.style.color = '#ffffff';
                child.style.fontFamily = '"Inter", "Segoe UI", system-ui, -apple-system, BlinkMacSystemFont, sans-serif';
            });
        });
    }

    // Apply footer styling
    function applyFooterStyling() {
        const footers = document.querySelectorAll('.footer, [class*="footer"], .bottom-bar, .layout-footer');
        footers.forEach(footer => {
            footer.style.background = '#2c3e50';
            footer.style.color = '#e2e8f0';
            footer.style.borderTop = '1px solid #475569';
            
            // Style all child elements
            const children = footer.querySelectorAll('*');
            children.forEach(child => {
                child.style.color = '#e2e8f0';
                child.style.fontFamily = '"Inter", "Segoe UI", system-ui, -apple-system, BlinkMacSystemFont, sans-serif';
            });
            
            // Style links
            const links = footer.querySelectorAll('a');
            links.forEach(link => {
                link.addEventListener('mouseenter', function() {
                    this.style.color = '#0069b3';
                });
                link.addEventListener('mouseleave', function() {
                    this.style.color = '#e2e8f0';
                });
            });
        });
    }

    // Apply typography
    function applyTypography() {
        // Apply font to body
        document.body.style.fontFamily = '"Inter", "Segoe UI", system-ui, -apple-system, BlinkMacSystemFont, sans-serif';
        document.body.style.fontSize = '0.875rem';
        document.body.style.lineHeight = '1.6';
        document.body.style.color = '#2c3e50';
        document.body.style.backgroundColor = '#f8fafc';
        
        // Apply font to all elements
        const allElements = document.querySelectorAll('*');
        allElements.forEach(element => {
            element.style.fontFamily = '"Inter", "Segoe UI", system-ui, -apple-system, BlinkMacSystemFont, sans-serif';
        });
        
        // Style headings
        const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
        headings.forEach(heading => {
            heading.style.color = '#1e293b';
            heading.style.fontWeight = '600';
            heading.style.fontFamily = '"Inter", "Segoe UI", system-ui, -apple-system, BlinkMacSystemFont, sans-serif';
        });
    }

    // Apply component styling
    function applyComponentStyling() {
        // Style primary buttons
        const primaryButtons = document.querySelectorAll('.btn-primary');
        primaryButtons.forEach(btn => {
            btn.style.background = 'linear-gradient(135deg, #0069b3, #0056a3)';
            btn.style.borderColor = '#0069b3';
            btn.style.color = '#ffffff';
        });

        // Style success buttons
        const successButtons = document.querySelectorAll('.btn-success');
        successButtons.forEach(btn => {
            btn.style.background = 'linear-gradient(135deg, #0baf4f, #099e46)';
            btn.style.borderColor = '#0baf4f';
        });

        // Style warning buttons
        const warningButtons = document.querySelectorAll('.btn-warning');
        warningButtons.forEach(btn => {
            btn.style.background = 'linear-gradient(135deg, #f7c000, #e6ad00)';
            btn.style.borderColor = '#f7c000';
        });

        // Style danger buttons
        const dangerButtons = document.querySelectorAll('.btn-danger');
        dangerButtons.forEach(btn => {
            btn.style.background = 'linear-gradient(135deg, #e61c23, #d1161d)';
            btn.style.borderColor = '#e61c23';
        });

        // Style cards
        const cards = document.querySelectorAll('.card');
        cards.forEach(card => {
            card.style.borderRadius = '0.75rem';
            card.style.boxShadow = '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)';
            card.style.border = '1px solid #e2e8f0';
        });

        // Style alerts
        const successAlerts = document.querySelectorAll('.alert-success');
        successAlerts.forEach(alert => {
            alert.style.backgroundColor = '#dcfce7';
            alert.style.borderColor = '#0baf4f';
            alert.style.color = '#166534';
        });

        const warningAlerts = document.querySelectorAll('.alert-warning');
        warningAlerts.forEach(alert => {
            alert.style.backgroundColor = '#fef3c7';
            alert.style.borderColor = '#f7c000';
            alert.style.color = '#92400e';
        });

        const dangerAlerts = document.querySelectorAll('.alert-danger');
        dangerAlerts.forEach(alert => {
            alert.style.backgroundColor = '#fee2e2';
            alert.style.borderColor = '#e61c23';
            alert.style.color = '#991b1b';
        });

        const infoAlerts = document.querySelectorAll('.alert-info');
        infoAlerts.forEach(alert => {
            alert.style.backgroundColor = '#e5f2f9';
            alert.style.borderColor = '#0069b3';
            alert.style.color = '#1e40af';
        });
    }

    // Function to monitor for theme changes
    function monitorThemeChanges() {
        // Check every 2 seconds
        setInterval(() => {
            if (isOldThemeActive()) {
                applyOldTheme();
            }
        }, 2000);

        // Listen for URL changes
        let currentUrl = window.location.href;
        setInterval(() => {
            if (window.location.href !== currentUrl) {
                currentUrl = window.location.href;
                setTimeout(applyOldTheme, 100);
            }
        }, 500);

        // Listen for storage changes
        window.addEventListener('storage', function(e) {
            if (e.key === 'active_theme') {
                setTimeout(applyOldTheme, 100);
            }
        });
    }

    // Initialize
    function init() {
        console.log('Old Theme Activator initialized');
        applyOldTheme();
        monitorThemeChanges();
        
        // Apply on DOM changes
        const observer = new MutationObserver(() => {
            if (isOldThemeActive()) {
                setTimeout(applyOldTheme, 100);
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // Run when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // Export for global access
    window.OldThemeActivator = {
        applyOldTheme,
        isOldThemeActive
    };

})();
