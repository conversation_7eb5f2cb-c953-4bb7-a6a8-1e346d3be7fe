/*!
 * Zoho-Inspired Chart Configurations
 * Enhanced chart styling for the classic theme
 */

// Zoho color palette for charts
const <PERSON><PERSON><PERSON><PERSON>hartColors = {
    primary: '#0069b3',
    secondary: '#e5f2f9',
    success: '#0baf4f',
    warning: '#f7c000',
    danger: '#e61c23',
    info: '#0069b3',
    light: '#f8fafc',
    dark: '#2c3e50',
    gradient: {
        primary: ['#0069b3', '#0056a3'],
        success: ['#0baf4f', '#099e46'],
        warning: ['#f7c000', '#e6ad00'],
        danger: ['#e61c23', '#d1161d'],
        info: ['#0069b3', '#0056a3']
    },
    palette: [
        '#0069b3', '#0baf4f', '#f7c000', '#e61c23', 
        '#6366f1', '#8b5cf6', '#ec4899', '#f97316',
        '#14b8a6', '#06b6d4', '#84cc16', '#eab308'
    ]
};

// Default chart configuration for Zoho theme
const <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s = {
    chart: {
        fontFamily: '"Inter", "Segoe UI", system-ui, -apple-system, sans-serif',
        foreColor: '#2c3e50',
        background: 'transparent',
        animations: {
            enabled: true,
            easing: 'easeinout',
            speed: 800,
            animateGradually: {
                enabled: true,
                delay: 150
            },
            dynamicAnimation: {
                enabled: true,
                speed: 350
            }
        },
        toolbar: {
            show: false
        },
        zoom: {
            enabled: false
        }
    },
    colors: ZohoChartColors.palette,
    dataLabels: {
        enabled: false,
        style: {
            fontSize: '12px',
            fontWeight: 500,
            colors: ['#2c3e50']
        }
    },
    grid: {
        borderColor: '#e2e8f0',
        strokeDashArray: 3,
        xaxis: {
            lines: {
                show: false
            }
        },
        yaxis: {
            lines: {
                show: true
            }
        },
        padding: {
            top: 0,
            right: 0,
            bottom: 0,
            left: 0
        }
    },
    xaxis: {
        axisBorder: {
            show: false
        },
        axisTicks: {
            show: false
        },
        labels: {
            style: {
                colors: '#64748b',
                fontSize: '12px',
                fontWeight: 500
            }
        }
    },
    yaxis: {
        labels: {
            style: {
                colors: '#64748b',
                fontSize: '12px',
                fontWeight: 500
            }
        }
    },
    legend: {
        position: 'bottom',
        horizontalAlign: 'center',
        fontSize: '12px',
        fontWeight: 500,
        labels: {
            colors: '#2c3e50'
        },
        markers: {
            width: 8,
            height: 8,
            radius: 4
        }
    },
    tooltip: {
        theme: 'light',
        style: {
            fontSize: '12px',
            fontFamily: '"Inter", "Segoe UI", system-ui, -apple-system, sans-serif'
        },
        x: {
            show: true
        },
        y: {
            formatter: function(val) {
                return typeof val === 'number' ? val.toLocaleString() : val;
            }
        }
    },
    stroke: {
        curve: 'smooth',
        width: 3
    },
    fill: {
        type: 'gradient',
        gradient: {
            shade: 'light',
            type: 'vertical',
            shadeIntensity: 0.5,
            gradientToColors: undefined,
            inverseColors: false,
            opacityFrom: 0.8,
            opacityTo: 0.6,
            stops: [0, 100]
        }
    }
};

// Dark mode chart configuration
const ZohoDarkChartDefaults = {
    ...ZohoChartDefaults,
    chart: {
        ...ZohoChartDefaults.chart,
        foreColor: '#e2e8f0'
    },
    grid: {
        ...ZohoChartDefaults.grid,
        borderColor: '#334155'
    },
    xaxis: {
        ...ZohoChartDefaults.xaxis,
        labels: {
            style: {
                colors: '#94a3b8',
                fontSize: '12px',
                fontWeight: 500
            }
        }
    },
    yaxis: {
        ...ZohoChartDefaults.yaxis,
        labels: {
            style: {
                colors: '#94a3b8',
                fontSize: '12px',
                fontWeight: 500
            }
        }
    },
    legend: {
        ...ZohoChartDefaults.legend,
        labels: {
            colors: '#e2e8f0'
        }
    },
    tooltip: {
        ...ZohoChartDefaults.tooltip,
        theme: 'dark'
    }
};

// Chart helper functions
const ZohoChartHelpers = {
    // Get appropriate chart defaults based on theme
    getDefaults() {
        const isDark = document.documentElement.getAttribute('data-theme') === 'dark' ||
                      document.body.classList.contains('dark-mode');
        return isDark ? ZohoDarkChartDefaults : ZohoChartDefaults;
    },

    // Merge custom options with defaults
    mergeOptions(customOptions) {
        const defaults = this.getDefaults();
        return this.deepMerge(defaults, customOptions);
    },

    // Deep merge objects
    deepMerge(target, source) {
        const output = Object.assign({}, target);
        if (this.isObject(target) && this.isObject(source)) {
            Object.keys(source).forEach(key => {
                if (this.isObject(source[key])) {
                    if (!(key in target))
                        Object.assign(output, { [key]: source[key] });
                    else
                        output[key] = this.deepMerge(target[key], source[key]);
                } else {
                    Object.assign(output, { [key]: source[key] });
                }
            });
        }
        return output;
    },

    isObject(item) {
        return item && typeof item === 'object' && !Array.isArray(item);
    },

    // Create gradient colors
    createGradient(color1, color2) {
        return {
            type: 'gradient',
            gradient: {
                shade: 'light',
                type: 'vertical',
                shadeIntensity: 0.5,
                gradientToColors: [color2],
                inverseColors: false,
                opacityFrom: 0.8,
                opacityTo: 0.6,
                stops: [0, 100]
            }
        };
    },

    // Format large numbers
    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    },

    // Create responsive chart options
    createResponsiveOptions(breakpoints = {}) {
        return [{
            breakpoint: 768,
            options: {
                chart: {
                    height: 300
                },
                legend: {
                    position: 'bottom'
                },
                ...breakpoints.mobile
            }
        }, {
            breakpoint: 1024,
            options: {
                chart: {
                    height: 350
                },
                ...breakpoints.tablet
            }
        }];
    }
};

// Example chart configurations
const ZohoChartExamples = {
    // Dashboard statistics donut chart
    createStatsDonut(data, labels) {
        return ZohoChartHelpers.mergeOptions({
            series: data,
            chart: {
                type: 'donut',
                height: 250
            },
            labels: labels,
            colors: [ZohoChartColors.primary, ZohoChartColors.success, ZohoChartColors.warning, ZohoChartColors.danger],
            plotOptions: {
                pie: {
                    donut: {
                        size: '70%',
                        labels: {
                            show: true,
                            total: {
                                show: true,
                                label: 'Total',
                                fontSize: '16px',
                                fontWeight: 600,
                                color: '#2c3e50'
                            }
                        }
                    }
                }
            },
            responsive: ZohoChartHelpers.createResponsiveOptions({
                mobile: {
                    chart: { height: 200 },
                    plotOptions: {
                        pie: {
                            donut: {
                                size: '60%'
                            }
                        }
                    }
                }
            })
        });
    },

    // Task progress bar chart
    createTaskProgress(categories, data) {
        return ZohoChartHelpers.mergeOptions({
            series: [{
                name: 'Tasks',
                data: data
            }],
            chart: {
                type: 'bar',
                height: 300,
                horizontal: true
            },
            xaxis: {
                categories: categories
            },
            colors: [ZohoChartColors.primary],
            plotOptions: {
                bar: {
                    borderRadius: 4,
                    horizontal: true,
                    barHeight: '70%'
                }
            }
        });
    },

    // Monthly activity line chart
    createActivityLine(categories, series) {
        return ZohoChartHelpers.mergeOptions({
            series: series,
            chart: {
                type: 'line',
                height: 350
            },
            xaxis: {
                categories: categories
            },
            colors: [ZohoChartColors.primary, ZohoChartColors.success],
            stroke: {
                width: 3,
                curve: 'smooth'
            },
            markers: {
                size: 6,
                strokeWidth: 2,
                strokeColors: '#ffffff',
                hover: {
                    size: 8
                }
            }
        });
    }
};

// Listen for theme changes and update charts
window.addEventListener('themeChanged', function(e) {
    // Trigger chart re-render with new theme
    window.dispatchEvent(new CustomEvent('chartsThemeUpdate', { 
        detail: { theme: e.detail.theme } 
    }));
});

// Export for global access
window.ZohoCharts = {
    Colors: ZohoChartColors,
    Defaults: ZohoChartDefaults,
    DarkDefaults: ZohoDarkChartDefaults,
    Helpers: ZohoChartHelpers,
    Examples: ZohoChartExamples
};
