/*!
 * Zoho-Inspired Theme JavaScript
 * Handles theme switching, animations, and dynamic UI features
 */

(function() {
    'use strict';

    // Theme management
    const ThemeManager = {
        init() {
            this.setupThemeToggle();
            this.setupAnimations();
            this.setupDashboardEnhancements();
            this.setupTaskListFeatures();
            this.setupNotificationPanel();
            this.applyStoredTheme();
        },

        setupThemeToggle() {
            const themeToggle = document.querySelector('[data-theme-toggle]');
            if (themeToggle) {
                themeToggle.addEventListener('click', this.toggleTheme.bind(this));
            }

            // Auto-detect system theme preference
            if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                this.setTheme('dark');
            }

            // Listen for system theme changes
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
                if (!localStorage.getItem('theme-preference')) {
                    this.setTheme(e.matches ? 'dark' : 'light');
                }
            });
        },

        toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            this.setTheme(newTheme);
        },

        setTheme(theme) {
            document.documentElement.setAttribute('data-theme', theme);
            document.body.classList.toggle('dark-mode', theme === 'dark');
            localStorage.setItem('theme-preference', theme);
            
            // Update theme toggle icon
            const themeToggle = document.querySelector('[data-theme-toggle]');
            if (themeToggle) {
                const icon = themeToggle.querySelector('i');
                if (icon) {
                    icon.className = theme === 'dark' ? 'bx bx-sun' : 'bx bx-moon';
                }
            }

            // Trigger custom event for other components
            window.dispatchEvent(new CustomEvent('themeChanged', { detail: { theme } }));
        },

        applyStoredTheme() {
            const storedTheme = localStorage.getItem('theme-preference');
            if (storedTheme) {
                this.setTheme(storedTheme);
            }
        },

        setupAnimations() {
            // Add entrance animations to cards
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // Observe dashboard cards
            document.querySelectorAll('.dashboard-card, .task-list, .calendar-container').forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(card);
            });
        },

        setupDashboardEnhancements() {
            // Animate dashboard statistics
            this.animateCounters();
            
            // Add hover effects to dashboard cards
            document.querySelectorAll('.dashboard-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-4px) scale(1.02)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        },

        animateCounters() {
            const counters = document.querySelectorAll('.dashboard-stat-number');
            
            counters.forEach(counter => {
                const target = parseInt(counter.textContent);
                const duration = 2000; // 2 seconds
                const step = target / (duration / 16); // 60fps
                let current = 0;
                
                const timer = setInterval(() => {
                    current += step;
                    if (current >= target) {
                        counter.textContent = target;
                        clearInterval(timer);
                    } else {
                        counter.textContent = Math.floor(current);
                    }
                }, 16);
            });
        },

        setupTaskListFeatures() {
            // Add drag and drop functionality
            this.setupTaskDragDrop();
            
            // Add task completion animations
            document.querySelectorAll('.task-item').forEach(task => {
                const checkbox = task.querySelector('input[type="checkbox"]');
                if (checkbox) {
                    checkbox.addEventListener('change', function() {
                        const taskItem = this.closest('.task-item');
                        if (this.checked) {
                            taskItem.classList.add('completed');
                            taskItem.style.animation = 'taskComplete 0.5s ease';
                        } else {
                            taskItem.classList.remove('completed');
                            taskItem.style.animation = '';
                        }
                    });
                }
            });
        },

        setupTaskDragDrop() {
            const taskItems = document.querySelectorAll('.task-item[draggable="true"]');
            
            taskItems.forEach(item => {
                item.addEventListener('dragstart', function(e) {
                    this.style.opacity = '0.5';
                    e.dataTransfer.setData('text/plain', this.dataset.taskId);
                });
                
                item.addEventListener('dragend', function() {
                    this.style.opacity = '1';
                });
                
                item.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    this.style.borderTop = '2px solid var(--zoho-primary)';
                });
                
                item.addEventListener('dragleave', function() {
                    this.style.borderTop = '';
                });
                
                item.addEventListener('drop', function(e) {
                    e.preventDefault();
                    this.style.borderTop = '';
                    const draggedId = e.dataTransfer.getData('text/plain');
                    // Handle task reordering here
                    console.log('Reorder task:', draggedId, 'before:', this.dataset.taskId);
                });
            });
        },

        setupNotificationPanel() {
            // Auto-hide notifications after 5 seconds
            document.querySelectorAll('.notification-item.unread').forEach(notification => {
                setTimeout(() => {
                    notification.classList.remove('unread');
                    notification.style.animation = 'fadeOut 0.3s ease';
                }, 5000);
            });
            
            // Add click to dismiss functionality
            document.querySelectorAll('.notification-item').forEach(notification => {
                notification.addEventListener('click', function() {
                    this.style.animation = 'slideOut 0.3s ease';
                    setTimeout(() => {
                        this.remove();
                    }, 300);
                });
            });
        }
    };

    // Utility functions
    const Utils = {
        // Debounce function for performance
        debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        // Format numbers with commas
        formatNumber(num) {
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        },

        // Generate random colors for charts
        generateChartColors(count) {
            const colors = [
                'var(--zoho-primary)',
                'var(--zoho-success)',
                'var(--zoho-warning)',
                'var(--zoho-danger)',
                '#6366f1',
                '#8b5cf6',
                '#ec4899',
                '#f97316'
            ];
            return colors.slice(0, count);
        }
    };

    // CSS animations
    const style = document.createElement('style');
    style.textContent = `
        @keyframes taskComplete {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }
        
        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .loading {
            animation: pulse 1.5s ease-in-out infinite;
        }
    `;
    document.head.appendChild(style);

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => ThemeManager.init());
    } else {
        ThemeManager.init();
    }

    // Export for global access
    window.ZohoTheme = {
        ThemeManager,
        Utils
    };

})();
